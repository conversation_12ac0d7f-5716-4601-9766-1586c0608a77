stages:
  - build
  - stable
  - notify

include:
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'templates/deploy-k8s.yaml'
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'jobs/notify-success.yaml'
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'jobs/notify-fail.yaml'

variables:
  GITOPS_APP_NAME: app-word-guess-backend

workflow:
  # rules的规则：匹配到第一个，后面的短路
  rules:
    # test分支时各变量设置为测试环境的值
    - if: '$CI_COMMIT_BRANCH == "test"'
      variables:
        IMAGE: registry.cn-shenzhen.aliyuncs.com/project5e-test/app-word-guess-backend
        GITOPS_ENVIRONMENT: test
        DOCKER_USERNAME: $TEST_DOCKER_USERNAME
        DOCKER_PASSWORD: $TEST_DOCKER_PASSWORD
        ENVIRONMENT_URL: https://apitest.wemore.com/english-so-so/swagger
    # main分支时各变量设置为生产环境的值; main的pipeline需要手动启动
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        IMAGE: registry-vpc.cn-beijing.aliyuncs.com/4tune/app-word-guess-backend
        GITOPS_ENVIRONMENT: prod
        DOCKER_USERNAME: $PROD_DOCKER_USERNAME
        DOCKER_PASSWORD: $PROD_DOCKER_PASSWORD
        ENVIRONMENT_URL: https://api.wemore.com/english-so-so/swagger

build:
  stage: build
  tags:
    - 4tune
    - beijing
    - prod
  script:
    - docker build -t ${IMAGE} .
    - docker tag ${IMAGE} ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker login --username=${DOCKER_USERNAME} --password=${DOCKER_PASSWORD} ${IMAGE}
    - docker push ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker rmi ${IMAGE}:${CI_COMMIT_SHORT_SHA}

deploy-stable:
  stage: stable
  tags:
    - 4tune
    - beijing
    - prod
  extends:
    - .deploy-stable
  environment:
    name: ${GITOPS_ENVIRONMENT}
    url: ${ENVIRONMENT_URL}
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: manual
