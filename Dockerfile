# 构建阶段
FROM registry.cn-beijing.aliyuncs.com/ergedd/golang:1.22 AS builder

ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct 

# 设置工作目录
WORKDIR /app

# 复制 go.mod 和 go.sum
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM registry.cn-beijing.aliyuncs.com/ergedd/alpine:3.9

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1001 -S appuser && \
    adduser -u 1001 -S appuser -G appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .
COPY --from=builder /app/data ./data

# 创建上传目录
RUN mkdir -p uploads && chown appuser:appuser uploads

# 切换到非 root 用户
USER appuser

# 暴露端口
EXPOSE 3000

# 运行应用
CMD ["./main"]
