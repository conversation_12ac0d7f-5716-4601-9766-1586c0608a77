.PHONY: build run clean test docker-build docker-run help

# 应用名称
APP_NAME=guess-word-backend
DOCKER_IMAGE=guess-word-backend:latest

# 默认目标
.DEFAULT_GOAL := help

# 构建应用
build:
	@echo "🔨 构建应用..."
	go build -o $(APP_NAME) .

# 运行应用
run:
	@echo "🚀 启动应用..."
	go run main.go

# 开发模式运行（自动重载）
dev:
	@echo "🔧 开发模式启动..."
	go run main.go

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	rm -f $(APP_NAME)
	rm -rf uploads/*

# 运行测试
test:
	@echo "🧪 运行测试..."
	go test -v ./...

# 格式化代码
fmt:
	@echo "📝 格式化代码..."
	go fmt ./...

# 代码检查
vet:
	@echo "🔍 代码检查..."
	go vet ./...

# 下载依赖
deps:
	@echo "📦 下载依赖..."
	go mod download
	go mod tidy

# 构建 Docker 镜像
docker-build:
	@echo "🐳 构建 Docker 镜像..."
	docker build -t $(DOCKER_IMAGE) .

# 运行 Docker 容器
docker-run:
	@echo "🐳 运行 Docker 容器..."
	docker run -d \
		--name $(APP_NAME) \
		-p 3000:3000 \
		--env-file .env \
		$(DOCKER_IMAGE)

# 停止 Docker 容器
docker-stop:
	@echo "🛑 停止 Docker 容器..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

# 查看 Docker 日志
docker-logs:
	@echo "📄 查看 Docker 日志..."
	docker logs -f $(APP_NAME)

# 代码质量检查
lint:
	@echo "🔧 代码质量检查..."
	golangci-lint run

# 性能测试
bench:
	@echo "⚡ 性能测试..."
	go test -bench=. -benchmem ./...

# 生成依赖图
deps-graph:
	@echo "📊 生成依赖图..."
	go mod graph

# 安全检查
security:
	@echo "🔒 安全检查..."
	gosec ./...

# 帮助信息
help:
	@echo "🎯 可用命令:"
	@echo "  build        构建应用"
	@echo "  run          运行应用"
	@echo "  dev          开发模式运行"
	@echo "  clean        清理构建文件"
	@echo "  test         运行测试"
	@echo "  fmt          格式化代码"
	@echo "  vet          代码检查"
	@echo "  deps         下载依赖"
	@echo "  docker-build 构建 Docker 镜像"
	@echo "  docker-run   运行 Docker 容器"
	@echo "  docker-stop  停止 Docker 容器"
	@echo "  docker-logs  查看 Docker 日志"
	@echo "  lint         代码质量检查"
	@echo "  bench        性能测试"
	@echo "  security     安全检查"
	@echo "  help         显示帮助信息"
