# 英语发音练习小程序后端

基于 Golang 开发的语音识别后端服务，集成火山引擎豆包 ASR API，为英语发音练习小程序提供语音识别和单词数据服务。

## 🚀 功能特性

- ✅ 语音识别（ASR）- 集成火山引擎豆包 API
- ✅ 单词数据管理
- ✅ 多种音频格式支持（MP3、WAV、M4A）
- ✅ 智能相似度匹配
- ✅ RESTful API 设计
- ✅ Docker 容器化支持
- ✅ 跨域 CORS 支持

## 📋 API 接口

### 语音识别接口

```
POST /asr
Content-Type: multipart/form-data

参数:
- audio: 音频文件 (MP3/WAV/M4A, 最大10MB)
- target_word: 目标单词
```

### 单词数据接口

```
GET /words                    # 获取所有单词
GET /words/random?count=5     # 获取随机单词
GET /words/search?q=apple     # 搜索单词
GET /words/difficulty/easy    # 按难度获取单词
```

## 🛠️ 开发环境设置

### 环境要求

- Go 1.21+
- Git

### 安装依赖

```bash
# 下载依赖
go mod download
```

### 配置环境变量

复制 `.env.example` 到 `.env` 并配置：

```env
# 火山引擎豆包 STT 配置
DOUBAO_STT_APP_ID=your_app_id
DOUBAO_STT_ACCESS_TOKEN=your_access_token
DOUBAO_STT_SECRET_KEY=your_secret_key

# 服务配置
PORT=3000
ENVIRONMENT=development
```

### 本地运行

```bash
# 开发模式运行
go run main.go

# 或者构建后运行
go build -o guess-word-backend
./guess-word-backend
```

服务将在 `http://localhost:3000` 启动。

## 🐳 Docker 部署

### 构建镜像

```bash
docker build -t guess-word-backend .
```

### 运行容器

```bash
docker run -d \
  --name guess-word-backend \
  -p 3000:3000 \
  --env-file .env \
  guess-word-backend
```

### 使用 Docker Compose

```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./uploads:/app/uploads
```

```bash
docker-compose up -d
```

## 📊 项目结构

```
guess-word-backend/
├── main.go                    # 应用入口
├── go.mod                     # Go 模块文件
├── go.sum                     # 依赖校验文件
├── Dockerfile                 # Docker 构建文件
├── .env                       # 环境变量配置
├── docs/                      # 文档目录
│   └── API_DOCUMENTATION.md   # API 文档
├── data/                      # 数据文件
│   └── words.json             # 示例单词数据
└── internal/                  # 内部包
    ├── config/                # 配置管理
    │   └── config.go
    ├── models/                # 数据模型
    │   └── models.go
    ├── services/              # 业务服务
    │   ├── asr_service.go     # 语音识别服务
    │   └── word_service.go    # 单词服务
    ├── handlers/              # HTTP 处理器
    │   ├── asr_handler.go     # ASR 接口处理
    │   └── word_handler.go    # 单词接口处理
    └── routes/                # 路由配置
        └── routes.go
```

## 🔧 火山引擎豆包 ASR 集成

### API 配置

本项目集成了火山引擎豆包的语音转文本（STT）服务：

1. **申请 API 密钥**：在火山引擎控制台申请 STT 服务
2. **配置环境变量**：将 API 密钥配置到 `.env` 文件
3. **音频格式**：支持 MP3、WAV、M4A 格式
4. **语言模型**：使用英语识别模型（en-US）

### 识别流程

1. 接收客户端上传的音频文件
2. 验证文件格式和大小
3. 调用豆包 STT API 进行语音识别
4. 计算识别结果与目标单词的相似度
5. 返回识别结果和置信度

## 🧪 测试

### 使用 curl 测试 ASR 接口

```bash
curl -X POST http://localhost:3000/asr \
  -F "audio=@test.mp3" \
  -F "target_word=apple"
```

### 测试单词接口

```bash
# 获取所有单词
curl http://localhost:3000/words

# 获取随机单词
curl http://localhost:3000/words/random?count=3

# 搜索单词
curl http://localhost:3000/words/search?q=apple

# 按难度获取单词
curl http://localhost:3000/words/difficulty/easy
```

## 📈 性能优化

- **并发处理**：使用 Goroutines 处理请求
- **文件限制**：限制上传文件大小（10MB）
- **内存管理**：及时清理临时文件
- **错误处理**：完善的错误响应机制

## 🔒 安全特性

- **文件验证**：严格验证上传文件类型和大小
- **CORS 配置**：支持跨域请求
- **错误隐藏**：不暴露敏感系统信息
- **输入验证**：验证所有输入参数

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。
