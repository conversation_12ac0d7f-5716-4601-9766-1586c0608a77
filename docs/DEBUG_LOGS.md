# ASR 服务调试日志说明

## 概述
为了便于在服务器上调试语音识别接口的问题，已在关键位置添加了详细的日志输出。

## 添加的日志功能

### 1. 应用启动日志 (`main.go`)
- 环境变量加载状态
- 配置加载完成信息
- Doubao API 配置状态（敏感信息已遮盖）
- 服务和处理器初始化状态
- Gin 模式设置
- Sentry 初始化状态
- CORS 配置完成
- 路由设置完成
- 服务器启动信息

### 2. 配置加载日志 (`internal/config/config.go`)
- 配置开始加载
- 必需配置项验证
- 配置加载完成状态
- Doubao 配置项状态检查

### 3. ASR Handler 日志 (`internal/handlers/asr_handler.go`)
- 请求接收（包含客户端IP）
- 音频文件信息（文件名、大小）
- 文件格式验证
- 文件大小检查
- 音频数据读取过程
- 目标单词参数
- 语音识别服务调用
- 最终结果返回

### 4. ASR Service 日志 (`internal/services/asr_service.go`)
- 语音识别开始（音频大小、目标单词）
- 豆包 STT API 调用详细过程：
  - base64 编码过程
  - 请求参数详情
  - JSON 编码状态
  - HTTP 请求创建
  - 请求头设置（Token 已遮盖）
  - 响应状态码
  - 响应内容
  - JSON 解析状态
- 相似度计算详情：
  - 输入文本对比
  - 匹配类型（精确/包含/编辑距离）
  - 编辑距离和相似度计算结果
- 置信度调整过程
- 最终识别结果

## 日志格式
所有日志都使用统一的格式：
```
[组件名] 操作描述...
```

例如：
- `[Main]` - 主程序
- `[Config]` - 配置加载
- `[ASR Handler]` - ASR 请求处理器
- `[ASR]` - ASR 服务

## 敏感信息保护
- API Token 和密钥会被遮盖显示（如：`abc1234567...`）
- 只显示前10个字符和省略号

## 使用建议

### 1. 启动时检查
启动应用时，检查以下日志确保配置正确：
```
[Config] 配置加载完成 - Environment: development, Port: 3000
[Config] Doubao配置状态 - AppID: 已设置, Token: 已设置
```

### 2. 请求调试
当 `/asr` 接口出现问题时，查找以下关键日志：
```
[ASR Handler] 收到语音识别请求，客户端IP: xxx
[ASR] 开始语音识别，音频数据大小: xxx bytes, 目标单词: xxx
[ASR] 豆包STT API调用成功，识别文本: xxx, 置信度: xxx
[ASR] 最终响应: Success=true, Text='xxx', Confidence=xxx
```

### 3. 错误诊断
如果出现错误，查看详细的错误日志：
- HTTP 请求失败：查看状态码和响应内容
- JSON 解析失败：查看原始响应内容
- 配置问题：查看配置加载日志

## 日志级别
当前所有日志都使用 `log.Printf()` 输出到标准输出，便于在服务器上通过日志文件查看。

## 监控建议
在生产环境中，可以通过以下方式监控：
1. 使用 `grep` 过滤特定组件的日志
2. 监控错误关键词（如 "失败"、"错误"、"Warning"）
3. 关注 API 响应状态码和耗时
