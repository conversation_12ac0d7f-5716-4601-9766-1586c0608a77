package config

import (
	"log"
	"os"
)

// Config 应用配置结构
type Config struct {
	Environment     string
	Port            string
	DoubaoSTTAppID  string
	DoubaoSTTToken  string
	DoubaoSTTSecret string
}

// Load 加载配置
func Load() *Config {
	log.Println("[Config] 开始加载配置...")

	config := &Config{
		Environment:     getEnv("ENVIRONMENT", "development"),
		Port:            getEnv("PORT", "3000"),
		DoubaoSTTAppID:  getEnv("DOUBAO_STT_APP_ID", ""),
		DoubaoSTTToken:  getEnv("DOUBAO_STT_ACCESS_TOKEN", ""),
		DoubaoSTTSecret: getEnv("DOUBAO_STT_SECRET_KEY", ""),
	}

	// 验证必需的配置项
	if config.DoubaoSTTAppID == "" {
		log.Println("[Config] Warning: DOUBAO_STT_APP_ID 未设置")
	}
	if config.DoubaoSTTToken == "" {
		log.Println("[Config] Warning: DOUBAO_STT_ACCESS_TOKEN 未设置")
	}

	log.Printf("[Config] 配置加载完成 - Environment: %s, Port: %s",
		config.Environment, config.Port)
	log.Printf("[Config] Doubao配置状态 - AppID: %s, Token: %s",
		boolToStatus(config.DoubaoSTTAppID != ""),
		boolToStatus(config.DoubaoSTTToken != ""))

	return config
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// boolToStatus 将布尔值转换为状态字符串
func boolToStatus(b bool) string {
	if b {
		return "已设置"
	}
	return "未设置"
}
