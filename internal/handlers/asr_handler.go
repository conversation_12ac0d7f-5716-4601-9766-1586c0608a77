package handlers

import (
	"io"
	"log"
	"net/http"
	"path/filepath"

	"guess-word-backend/internal/models"
	"guess-word-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// ASRHandler ASR 请求处理器
type ASRHandler struct {
	asrService *services.ASRService
}

// NewASRHandler 创建新的 ASR 处理器
func NewASRHandler(asrService *services.ASRService) *ASRHandler {
	return &ASRHandler{
		asrService: asrService,
	}
}

// HandleASR 处理语音识别请求
func (h *ASRHandler) HandleASR(c *gin.Context) {
	log.Printf("[ASR Handler] 收到语音识别请求，客户端IP: %s", c.ClientIP())

	// 获取上传的音频文件
	file, err := c.FormFile("audio")
	if err != nil {
		log.Printf("[ASR Handler] 未找到音频文件: %v", err)
		c.<PERSON>(http.StatusBadRequest, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "未找到音频文件",
			Code:       "MISSING_AUDIO_FILE",
		})
		return
	}

	log.Printf("[ASR Handler] 收到音频文件: %s, 大小: %d bytes", file.Filename, file.Size)

	// 检查文件类型
	ext := filepath.Ext(file.Filename)
	if ext != ".mp3" && ext != ".wav" && ext != ".m4a" {
		log.Printf("[ASR Handler] 不支持的音频格式: %s", ext)
		c.JSON(http.StatusBadRequest, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "不支持的音频格式，仅支持 MP3、WAV、M4A",
			Code:       "INVALID_AUDIO_FORMAT",
		})
		return
	}

	// 检查文件大小 (10MB 限制)
	if file.Size > 10*1024*1024 {
		log.Printf("[ASR Handler] 音频文件过大: %d bytes", file.Size)
		c.JSON(http.StatusBadRequest, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "音频文件过大，最大支持 10MB",
			Code:       "AUDIO_TOO_LARGE",
		})
		return
	}

	// 读取文件内容
	log.Printf("[ASR Handler] 开始读取音频文件内容...")
	fileReader, err := file.Open()
	if err != nil {
		log.Printf("[ASR Handler] 打开音频文件失败: %v", err)
		c.JSON(http.StatusInternalServerError, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "读取音频文件失败",
			Code:       "FILE_READ_ERROR",
		})
		return
	}
	defer fileReader.Close()

	audioData, err := io.ReadAll(fileReader)
	if err != nil {
		log.Printf("[ASR Handler] 读取音频数据失败: %v", err)
		c.JSON(http.StatusInternalServerError, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "读取音频数据失败",
			Code:       "AUDIO_READ_ERROR",
		})
		return
	}

	log.Printf("[ASR Handler] 音频数据读取成功，大小: %d bytes", len(audioData))

	// 获取目标单词
	targetWord := c.PostForm("target_word")
	if targetWord == "" {
		log.Printf("[ASR Handler] 缺少目标单词参数")
		c.JSON(http.StatusBadRequest, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "缺少目标单词参数",
			Code:       "MISSING_TARGET_WORD",
		})
		return
	}

	log.Printf("[ASR Handler] 目标单词: %s", targetWord)

	// 调用语音识别服务
	log.Printf("[ASR Handler] 开始调用语音识别服务...")
	result, err := h.asrService.RecognizeSpeech(c.Request.Context(), audioData, targetWord)
	if err != nil {
		log.Printf("[ASR Handler] 语音识别服务调用失败: %v", err)
		c.JSON(http.StatusInternalServerError, models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      "语音识别服务错误",
			Code:       "ASR_SERVICE_ERROR",
		})
		return
	}

	log.Printf("[ASR Handler] 语音识别完成，返回结果: Success=%t, Text='%s', Confidence=%.2f",
		result.Success, result.Text, result.Confidence)
	c.JSON(http.StatusOK, result)
}

// HandleHealth 健康检查
func (h *ASRHandler) HandleHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "asr",
		"message": "语音识别服务运行正常",
	})
}
