package handlers

import (
	"net/http"
	"strconv"

	"guess-word-backend/internal/models"
	"guess-word-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// WordHandler 单词请求处理器
type WordHandler struct {
	wordService *services.WordService
}

// NewWordHandler 创建新的单词处理器
func NewWordHandler(wordService *services.WordService) *WordHandler {
	return &WordHandler{
		wordService: wordService,
	}
}

// HandleGetAllWords 获取所有单词
func (h *WordHandler) HandleGetAllWords(c *gin.Context) {
	result := h.wordService.GetAllWords()
	c.JSON(http.StatusOK, result)
}

// HandleGetRandomWords 获取随机单词
func (h *WordHandler) HandleGetRandomWords(c *gin.Context) {
	countStr := c.Query("count")
	count := 5 // 默认值

	if countStr != "" {
		if parsedCount, err := strconv.Atoi(countStr); err == nil && parsedCount > 0 {
			count = parsedCount
		}
	}

	result := h.wordService.GetRandomWords(count)
	c.JSON(http.StatusOK, result)
}

// HandleSearchWords 搜索单词
func (h *WordHandler) HandleSearchWords(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Error:   "缺少搜索关键词",
			Code:    "MISSING_QUERY",
		})
		return
	}

	result := h.wordService.SearchWords(query)
	c.JSON(http.StatusOK, result)
}

// HandleGetWordsByDifficulty 按难度获取单词
func (h *WordHandler) HandleGetWordsByDifficulty(c *gin.Context) {
	difficulty := c.Param("level")
	if difficulty == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Error:   "缺少难度参数",
			Code:    "MISSING_DIFFICULTY",
		})
		return
	}

	// 验证难度参数
	validDifficulties := map[string]bool{
		"easy":   true,
		"medium": true,
		"hard":   true,
	}

	if !validDifficulties[difficulty] {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Error:   "无效的难度参数，支持: easy, medium, hard",
			Code:    "INVALID_DIFFICULTY",
		})
		return
	}

	result := h.wordService.GetWordsByDifficulty(difficulty)
	c.JSON(http.StatusOK, result)
}
