package models

// ASRRequest 语音识别请求参数
type ASRRequest struct {
	TargetWord string `form:"target_word" binding:"required"`
}

// ASRResponse 语音识别响应
type ASRResponse struct {
	Success    bool    `json:"success"`
	Text       string  `json:"text"`
	Confidence float64 `json:"confidence"`
	Message    string  `json:"message,omitempty"`
	Error      string  `json:"error,omitempty"`
	Code       string  `json:"code,omitempty"`
}

// Word 单词模型
type Word struct {
	ID            int    `json:"id"`
	Word          string `json:"word"`
	Image         string `json:"image"`
	Pronunciation string `json:"pronunciation"`
	Meaning       string `json:"meaning"`
	Difficulty    string `json:"difficulty"`
	Category      string `json:"category"`
}

// WordsResponse 单词列表响应
type WordsResponse struct {
	Words []Word `json:"words"`
	Total int    `json:"total"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Code    string `json:"code"`
}
