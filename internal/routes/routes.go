package routes

import (
	"log"
	"net/http"

	"guess-word-backend/internal/handlers"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes(router *gin.Engine, asrHandler *handlers.ASRHandler, wordHandler *handlers.WordHandler) {
	log.Println("[Routes] 开始设置路由...")

	// 全局健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "服务运行正常",
		})
	})
	log.Println("[Routes] 注册路由: GET /health")

	// 选择路由策略：使用新版本API路径或兼容旧版本
	// 这里我们选择使用新版本的 /api/v1 路径，并保留一个兼容的旧路径

	// 兼容旧版本：只保留主要的ASR接口
	router.POST("/asr", asrHandler.HandleASR)
	log.Println("[Routes] 注册路由: POST /asr (兼容旧版本)")

	// API v1 路由组 - 推荐使用的新版本API
	v1 := router.Group("/api/v1")
	{
		// 语音识别接口
		v1.POST("/asr", asrHandler.HandleASR)
		v1.GET("/asr/health", asrHandler.HandleHealth)
		log.Println("[Routes] 注册路由: POST /api/v1/asr")
		log.Println("[Routes] 注册路由: GET /api/v1/asr/health")

		// 单词相关接口
		words := v1.Group("/words")
		{
			words.GET("", wordHandler.HandleGetAllWords)
			words.GET("/random", wordHandler.HandleGetRandomWords)
			words.GET("/search", wordHandler.HandleSearchWords)
			words.GET("/difficulty/:level", wordHandler.HandleGetWordsByDifficulty)
		}
		log.Println("[Routes] 注册路由组: /api/v1/words/*")
	}

	log.Println("[Routes] 路由设置完成")
	log.Println("[Routes] 可用的API端点:")
	log.Println("[Routes]   - GET  /health")
	log.Println("[Routes]   - POST /asr (兼容旧版本)")
	log.Println("[Routes]   - POST /api/v1/asr (推荐)")
	log.Println("[Routes]   - GET  /api/v1/asr/health")
	log.Println("[Routes]   - GET  /api/v1/words")
	log.Println("[Routes]   - GET  /api/v1/words/random")
	log.Println("[Routes]   - GET  /api/v1/words/search")
	log.Println("[Routes]   - GET  /api/v1/words/difficulty/:level")
}
