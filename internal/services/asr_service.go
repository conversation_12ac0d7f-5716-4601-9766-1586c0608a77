package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"guess-word-backend/internal/config"
	"guess-word-backend/internal/models"
)

// ASRService 语音识别服务
type ASRService struct {
	config *config.Config
	client *http.Client
}

// NewASRService 创建新的 ASR 服务实例
func NewASRService(cfg *config.Config) *ASRService {
	return &ASRService{
		config: cfg,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// DoubaoSTTRequest 豆包 STT API 请求结构
type DoubaoSTTRequest struct {
	AppID       string `json:"app_id"`
	Format      string `json:"format"`
	Language    string `json:"language"`
	SampleRate  int    `json:"sample_rate"`
	EnableWords bool   `json:"enable_words"`
}

// DoubaoSTTResponse 豆包 STT API 响应结构
type DoubaoSTTResponse struct {
	Message   string `json:"message"`
	Code      int    `json:"code"`
	RequestID string `json:"request_id"`
	Result    struct {
		Text       string  `json:"text"`
		Confidence float64 `json:"confidence"`
		Words      []struct {
			Text       string  `json:"text"`
			StartTime  int     `json:"start_time"`
			EndTime    int     `json:"end_time"`
			Confidence float64 `json:"confidence"`
		} `json:"words"`
	} `json:"result"`
}

// RecognizeSpeech 识别语音
func (s *ASRService) RecognizeSpeech(ctx context.Context, audioData []byte, targetWord string) (*models.ASRResponse, error) {
	log.Printf("[ASR] 开始语音识别，音频数据大小: %d bytes, 目标单词: %s", len(audioData), targetWord)

	// 调用豆包 STT API
	result, err := s.callDoubaoSTT(ctx, audioData)
	if err != nil {
		log.Printf("[ASR] 豆包STT API调用失败: %v", err)
		return &models.ASRResponse{
			Success:    false,
			Text:       "",
			Confidence: 0,
			Error:      fmt.Sprintf("语音识别失败: %v", err),
			Code:       "RECOGNITION_FAILED",
		}, nil
	}

	log.Printf("[ASR] 豆包STT API调用成功，识别文本: %s, 置信度: %.2f", result.Result.Text, result.Result.Confidence)

	// 计算匹配度
	recognizedText := strings.ToLower(strings.TrimSpace(result.Result.Text))
	targetWordLower := strings.ToLower(strings.TrimSpace(targetWord))

	log.Printf("[ASR] 文本标准化 - 识别结果: '%s', 目标单词: '%s'", recognizedText, targetWordLower)

	// 简单的相似度判断
	confidence := s.calculateSimilarity(recognizedText, targetWordLower)
	log.Printf("[ASR] 相似度计算结果: %.2f", confidence)

	// 如果豆包返回了置信度，可以结合使用
	if result.Result.Confidence > 0 {
		oldConfidence := confidence
		confidence = (confidence + result.Result.Confidence) / 2
		log.Printf("[ASR] 结合豆包置信度，从 %.2f 调整为 %.2f", oldConfidence, confidence)
	}

	response := &models.ASRResponse{
		Success:    true,
		Text:       result.Result.Text,
		Confidence: confidence,
		Message:    "识别成功",
	}

	log.Printf("[ASR] 最终响应: Success=%t, Text='%s', Confidence=%.2f",
		response.Success, response.Text, response.Confidence)

	return response, nil
}

// callDoubaoSTT 调用豆包 STT API
func (s *ASRService) callDoubaoSTT(ctx context.Context, audioData []byte) (*DoubaoSTTResponse, error) {
	log.Printf("[ASR] 开始调用豆包STT API，音频数据大小: %d bytes", len(audioData))

	// 豆包 STT API 端点
	url := "https://openspeech.bytedance.com/api/v1/asr"
	log.Printf("[ASR] API端点: %s", url)

	// 创建 multipart form data
	log.Printf("[ASR] 创建multipart form data...")
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加音频文件
	log.Printf("[ASR] 添加音频文件...")
	audioWriter, err := writer.CreateFormFile("audio", "audio.mp3")
	if err != nil {
		log.Printf("[ASR] 创建音频字段失败: %v", err)
		return nil, fmt.Errorf("创建音频字段失败: %v", err)
	}
	_, err = audioWriter.Write(audioData)
	if err != nil {
		log.Printf("[ASR] 写入音频数据失败: %v", err)
		return nil, fmt.Errorf("写入音频数据失败: %v", err)
	}

	// 添加其他参数
	log.Printf("[ASR] 添加请求参数...")
	err = writer.WriteField("app_id", s.config.DoubaoSTTAppID)
	if err != nil {
		log.Printf("[ASR] 写入app_id失败: %v", err)
		return nil, fmt.Errorf("写入app_id失败: %v", err)
	}

	err = writer.WriteField("format", "mp3")
	if err != nil {
		log.Printf("[ASR] 写入format失败: %v", err)
		return nil, fmt.Errorf("写入format失败: %v", err)
	}

	err = writer.WriteField("language", "en-US")
	if err != nil {
		log.Printf("[ASR] 写入language失败: %v", err)
		return nil, fmt.Errorf("写入language失败: %v", err)
	}

	// 使用strconv将数字转换为字符串
	err = writer.WriteField("sample_rate", strconv.Itoa(16000))
	if err != nil {
		log.Printf("[ASR] 写入sample_rate失败: %v", err)
		return nil, fmt.Errorf("写入sample_rate失败: %v", err)
	}

	err = writer.WriteField("enable_words", "true")
	if err != nil {
		log.Printf("[ASR] 写入enable_words失败: %v", err)
		return nil, fmt.Errorf("写入enable_words失败: %v", err)
	}

	writer.Close()

	log.Printf("[ASR] 请求参数 - AppID: %s, Format: mp3, Language: en-US, SampleRate: 16000, EnableWords: true",
		s.config.DoubaoSTTAppID)
	log.Printf("[ASR] multipart form data创建完成，数据大小: %d bytes", buf.Len())

	// 创建请求
	log.Printf("[ASR] 创建HTTP请求...")
	req, err := http.NewRequestWithContext(ctx, "POST", url, &buf)
	if err != nil {
		log.Printf("[ASR] 创建请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+s.config.DoubaoSTTToken)
	log.Printf("[ASR] 请求头设置完成，Content-Type: %s, Authorization: Bearer %s...",
		writer.FormDataContentType(), s.maskToken(s.config.DoubaoSTTToken))

	// 发送请求
	log.Printf("[ASR] 发送HTTP请求...")
	resp, err := s.client.Do(req)
	if err != nil {
		log.Printf("[ASR] 发送请求失败: %v", err)
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	log.Printf("[ASR] 收到响应，状态码: %d", resp.StatusCode)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[ASR] 读取响应失败: %v", err)
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	log.Printf("[ASR] 响应体大小: %d bytes", len(body))
	log.Printf("[ASR] 响应内容: %s", string(body))

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		log.Printf("[ASR] API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	log.Printf("[ASR] 开始解析JSON响应...")
	var result DoubaoSTTResponse
	if err := json.Unmarshal(body, &result); err != nil {
		log.Printf("[ASR] 解析响应失败: %v", err)
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	log.Printf("[ASR] JSON解析成功，Code: %d, Message: %s, RequestID: %s",
		result.Code, result.Message, result.RequestID)

	// 检查业务状态码
	if result.Code != 0 {
		log.Printf("[ASR] 豆包API返回错误，Code: %d, Message: %s", result.Code, result.Message)
		return nil, fmt.Errorf("豆包 API 错误: %s (code: %d)", result.Message, result.Code)
	}

	log.Printf("[ASR] 豆包STT API调用成功")
	return &result, nil
}

// calculateSimilarity 计算文本相似度
func (s *ASRService) calculateSimilarity(text1, text2 string) float64 {
	log.Printf("[ASR] 计算相似度 - 文本1: '%s', 文本2: '%s'", text1, text2)

	// 精确匹配
	if text1 == text2 {
		log.Printf("[ASR] 精确匹配，相似度: 1.0")
		return 1.0
	}

	// 包含匹配
	if strings.Contains(text1, text2) || strings.Contains(text2, text1) {
		log.Printf("[ASR] 包含匹配，相似度: 0.8")
		return 0.8
	}

	// 编辑距离匹配（简化版本）
	distance := s.levenshteinDistance(text1, text2)
	maxLen := len(text1)
	if len(text2) > maxLen {
		maxLen = len(text2)
	}

	if maxLen == 0 {
		log.Printf("[ASR] 两个文本都为空，相似度: 1.0")
		return 1.0
	}

	similarity := 1.0 - float64(distance)/float64(maxLen)
	if similarity < 0 {
		similarity = 0
	}

	log.Printf("[ASR] 编辑距离匹配 - 距离: %d, 最大长度: %d, 相似度: %.2f",
		distance, maxLen, similarity)

	return similarity
}

// levenshteinDistance 计算编辑距离
func (s *ASRService) levenshteinDistance(s1, s2 string) int {
	len1, len2 := len(s1), len(s2)
	if len1 == 0 {
		return len2
	}
	if len2 == 0 {
		return len1
	}

	matrix := make([][]int, len1+1)
	for i := range matrix {
		matrix[i] = make([]int, len2+1)
		matrix[i][0] = i
	}
	for j := 1; j <= len2; j++ {
		matrix[0][j] = j
	}

	for i := 1; i <= len1; i++ {
		for j := 1; j <= len2; j++ {
			cost := 0
			if s1[i-1] != s2[j-1] {
				cost = 1
			}
			matrix[i][j] = min(
				matrix[i-1][j]+1,      // deletion
				matrix[i][j-1]+1,      // insertion
				matrix[i-1][j-1]+cost, // substitution
			)
		}
	}

	return matrix[len1][len2]
}

// min 返回三个数的最小值
func min(a, b, c int) int {
	if a < b && a < c {
		return a
	}
	if b < c {
		return b
	}
	return c
}

// minInt 返回两个数的最小值
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// maskToken 遮盖token用于日志输出
func (s *ASRService) maskToken(token string) string {
	if len(token) <= 10 {
		return strings.Repeat("*", len(token))
	}
	return token[:10] + "..."
}
