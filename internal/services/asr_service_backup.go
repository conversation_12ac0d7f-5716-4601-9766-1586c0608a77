package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strconv"
	"time"

	"guess-word-backend/internal/config"
)

// ASRServiceBackup 备用的ASR服务实现（使用multipart格式）
type ASRServiceBackup struct {
	config *config.Config
	client *http.Client
}

// NewASRServiceBackup 创建新的备用 ASR 服务实例
func NewASRServiceBackup(cfg *config.Config) *ASRServiceBackup {
	return &ASRServiceBackup{
		config: cfg,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// callDoubaoSTTMultipart 使用multipart格式调用豆包 STT API
func (s *ASRServiceBackup) callDoubaoSTTMultipart(ctx context.Context, audioData []byte) (*DoubaoSTTResponse, error) {
	// 豆包 STT API 端点
	url := "https://openspeech.bytedance.com/api/v1/asr"

	// 创建 multipart form data
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加音频文件
	audioWriter, err := writer.CreateFormFile("audio", "audio.mp3")
	if err != nil {
		return nil, fmt.Errorf("创建音频字段失败: %v", err)
	}
	_, err = audioWriter.Write(audioData)
	if err != nil {
		return nil, fmt.Errorf("写入音频数据失败: %v", err)
	}

	// 添加其他参数 - 正确处理数值类型
	err = writer.WriteField("app_id", s.config.DoubaoSTTAppID)
	if err != nil {
		return nil, fmt.Errorf("写入app_id失败: %v", err)
	}

	err = writer.WriteField("format", "mp3")
	if err != nil {
		return nil, fmt.Errorf("写入format失败: %v", err)
	}

	err = writer.WriteField("language", "en-US")
	if err != nil {
		return nil, fmt.Errorf("写入language失败: %v", err)
	}

	// 使用strconv将数字转换为字符串
	err = writer.WriteField("sample_rate", strconv.Itoa(16000))
	if err != nil {
		return nil, fmt.Errorf("写入sample_rate失败: %v", err)
	}

	err = writer.WriteField("enable_words", "true")
	if err != nil {
		return nil, fmt.Errorf("写入enable_words失败: %v", err)
	}

	writer.Close()

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, &buf)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+s.config.DoubaoSTTToken)

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API 请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var result DoubaoSTTResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查业务状态码
	if result.Code != 0 {
		return nil, fmt.Errorf("豆包 API 错误: %s (code: %d)", result.Message, result.Code)
	}

	return &result, nil
}
