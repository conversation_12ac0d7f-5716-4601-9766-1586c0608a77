package services

import (
	"math/rand"
	"strings"
	"time"

	"guess-word-backend/internal/models"
)

// WordService 单词服务
type WordService struct {
	words []models.Word
}

// NewWordService 创建新的单词服务实例
func NewWordService() *WordService {
	// 初始化示例单词数据
	words := []models.Word{
		{
			ID:            1,
			Word:          "apple",
			Image:         "https://example.com/images/apple.jpg",
			Pronunciation: "/ˈæpl/",
			Meaning:       "苹果",
			Difficulty:    "easy",
			Category:      "fruit",
		},
		{
			ID:            2,
			Word:          "banana",
			Image:         "https://example.com/images/banana.jpg",
			Pronunciation: "/bəˈnænə/",
			Meaning:       "香蕉",
			Difficulty:    "easy",
			Category:      "fruit",
		},
		{
			ID:            3,
			Word:          "elephant",
			Image:         "https://example.com/images/elephant.jpg",
			Pronunciation: "/ˈelɪfənt/",
			Meaning:       "大象",
			Difficulty:    "medium",
			Category:      "animal",
		},
		{
			ID:            4,
			Word:          "butterfly",
			Image:         "https://example.com/images/butterfly.jpg",
			Pronunciation: "/ˈbʌtərflaɪ/",
			Meaning:       "蝴蝶",
			Difficulty:    "hard",
			Category:      "animal",
		},
		{
			ID:            5,
			Word:          "computer",
			Image:         "https://example.com/images/computer.jpg",
			Pronunciation: "/kəmˈpjuːtər/",
			Meaning:       "电脑",
			Difficulty:    "medium",
			Category:      "technology",
		},
	}

	return &WordService{
		words: words,
	}
}

// GetAllWords 获取所有单词
func (s *WordService) GetAllWords() models.WordsResponse {
	return models.WordsResponse{
		Words: s.words,
		Total: len(s.words),
	}
}

// GetRandomWords 获取随机单词
func (s *WordService) GetRandomWords(count int) models.WordsResponse {
	if count <= 0 {
		count = 5
	}

	// 创建副本并打乱
	wordsCopy := make([]models.Word, len(s.words))
	copy(wordsCopy, s.words)

	// 使用当前时间作为随机种子
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(wordsCopy), func(i, j int) {
		wordsCopy[i], wordsCopy[j] = wordsCopy[j], wordsCopy[i]
	})

	// 取前 count 个
	if count > len(wordsCopy) {
		count = len(wordsCopy)
	}

	return models.WordsResponse{
		Words: wordsCopy[:count],
		Total: count,
	}
}

// SearchWords 搜索单词
func (s *WordService) SearchWords(query string) models.WordsResponse {
	query = strings.ToLower(query)
	var results []models.Word

	for _, word := range s.words {
		if strings.Contains(strings.ToLower(word.Word), query) ||
			strings.Contains(strings.ToLower(word.Meaning), query) ||
			strings.Contains(strings.ToLower(word.Category), query) {
			results = append(results, word)
		}
	}

	return models.WordsResponse{
		Words: results,
		Total: len(results),
	}
}

// GetWordsByDifficulty 按难度获取单词
func (s *WordService) GetWordsByDifficulty(difficulty string) models.WordsResponse {
	difficulty = strings.ToLower(difficulty)
	var results []models.Word

	for _, word := range s.words {
		if strings.ToLower(word.Difficulty) == difficulty {
			results = append(results, word)
		}
	}

	return models.WordsResponse{
		Words: results,
		Total: len(results),
	}
}
