package main

import (
	"fmt"
	"io"
	"log"
	"os"

	"guess-word-backend/internal/config"
	"guess-word-backend/internal/handlers"
	"guess-word-backend/internal/routes"
	"guess-word-backend/internal/services"

	"github.com/getsentry/sentry-go"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func setupLogging() {
	// 设置日志格式，包含文件名和行号
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 创建logs目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		fmt.Printf("创建logs目录失败: %v\n", err)
	}

	// 创建日志文件
	logFile, err := os.OpenFile("logs/app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Printf("创建日志文件失败: %v，只输出到控制台\n", err)
		log.SetOutput(os.Stdout)
	} else {
		// 同时输出到控制台和文件
		multiWriter := io.MultiWriter(os.Stdout, logFile)
		log.SetOutput(multiWriter)
		fmt.Println("日志将同时输出到控制台和 logs/app.log")
	}
}

func main() {
	// 首先设置日志
	setupLogging()

	// 强制输出，确保能看到
	fmt.Println("==================== 应用启动 ====================")
	log.Println("[Main] 启动应用程序...")

	// 加载环境变量
	log.Println("[Main] 加载环境变量...")
	if err := godotenv.Load(); err != nil {
		log.Println("[Main] Warning: .env file not found")
	}

	// 初始化配置
	log.Println("[Main] 初始化配置...")
	cfg := config.Load()
	log.Printf("[Main] 配置加载完成 - Environment: %s, Port: %s", cfg.Environment, cfg.Port)
	log.Printf("[Main] Doubao配置 - AppID: %s, Token: %s",
		cfg.DoubaoSTTAppID, maskString(cfg.DoubaoSTTToken))

	// 初始化服务
	log.Println("[Main] 初始化服务...")
	asrService := services.NewASRService(cfg)
	wordService := services.NewWordService()
	log.Println("[Main] 服务初始化完成")

	// 初始化处理器
	log.Println("[Main] 初始化处理器...")
	asrHandler := handlers.NewASRHandler(asrService)
	wordHandler := handlers.NewWordHandler(wordService)
	log.Println("[Main] 处理器初始化完成")

	// 设置 Gin 模式
	if cfg.Environment == "production" {
		log.Println("[Main] 设置为生产模式")
		gin.SetMode(gin.ReleaseMode)
	} else {
		log.Println("[Main] 设置为开发模式")
	}

	// 创建路由器
	log.Println("[Main] 创建路由器...")
	router := gin.Default()

	// 初始化 Sentry
	log.Println("[Main] 初始化 Sentry...")
	if err := sentry.Init(sentry.ClientOptions{
		Dsn: "https://<EMAIL>/49",
		// 或者从环境变量中读取
		// Dsn: os.Getenv("SENTRY_DSN"),
		TracesSampleRate: 1.0,
		Environment:      cfg.Environment,
	}); err != nil {
		log.Fatalf("[Main] Sentry 初始化失败: %v", err)
	}
	log.Println("[Main] Sentry 初始化成功")

	// 使用 Sentry 中间件
	router.Use(sentrygin.New(sentrygin.Options{}))

	// 配置 CORS
	log.Println("[Main] 配置 CORS...")
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	router.Use(cors.New(corsConfig))
	log.Println("[Main] CORS 配置完成")

	// 设置路由
	log.Println("[Main] 设置路由...")
	routes.SetupRoutes(router, asrHandler, wordHandler)

	// 添加 Sentry 测试路由
	router.GET("/sentry-test", func(c *gin.Context) {
		panic("This is a test panic from the Sentry integration!")
	})
	log.Println("[Main] 路由设置完成")

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}

	log.Printf("[Main] 🚀 服务器启动在端口 %s", port)
	log.Printf("[Main] 📡 语音识别接口: http://localhost:%s/asr", port)
	log.Printf("[Main] 📚 单词数据接口: http://localhost:%s/words", port)

	if err := router.Run(":" + port); err != nil {
		log.Fatal("[Main] 服务器启动失败:", err)
	}
}

// maskString 遮盖敏感字符串用于日志输出
func maskString(s string) string {
	if len(s) <= 10 {
		return "***"
	}
	return s[:10] + "..."
}
