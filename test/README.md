# ASR 本地测试工具

这个测试工具可以让你在本地测试ASR（语音识别）功能，无需部署到服务器。

## 使用步骤

### 1. 配置环境变量

复制环境变量示例文件：
```bash
cd test/
cp .env.example .env.test
```

编辑 `.env.test` 文件，填入你的真实配置：
```bash
# 豆包STT配置
export DOUBAO_STT_APP_ID="your_real_app_id"
export DOUBAO_STT_ACCESS_TOKEN="your_real_access_token"  
export DOUBAO_STT_SECRET_KEY="your_real_secret_key"

# 其他配置（可选）
export ENVIRONMENT="test"
export PORT="3000"
```

### 2. 准备测试音频文件

在 `test/` 目录下放置一个名为 `test_audio.mp3` 的音频文件。

音频要求：
- 格式：MP3
- 语言：英文
- 内容：清晰的单词发音
- 建议时长：1-5秒

### 3. 运行测试

#### 方法1：使用脚本（推荐）
```bash
cd test/
./run_test.sh
```

#### 方法2：直接运行Go程序
```bash
# 先加载环境变量
source test/.env.test

# 运行测试
go run test/asr_test.go
```

#### 方法3：手动设置环境变量
```bash
export DOUBAO_STT_APP_ID="your_app_id"
export DOUBAO_STT_ACCESS_TOKEN="your_token"
go run test/asr_test.go
```

## 测试结果说明

测试脚本会输出以下信息：

1. **配置验证**：检查环境变量是否正确设置
2. **音频文件信息**：显示音频文件大小
3. **识别结果**：
   - 识别成功/失败
   - 识别出的文本
   - 置信度（0-1之间，越高越好）
4. **性能信息**：API调用耗时
5. **评估**：基于置信度的效果评估

## 自定义测试

你可以修改 `asr_test.go` 中的以下参数：

```go
// 测试音频文件路径
audioFilePath := "test_audio.mp3"

// 目标单词（用于计算相似度）
targetWord := "hello"
```

## 常见问题

### Q: 提示环境变量未设置怎么办？
A: 确保 `.env.test` 文件存在且配置正确，使用 `source .env.test` 加载环境变量。

### Q: 提示音频文件不存在怎么办？
A: 在 `test/` 目录下放置 `test_audio.mp3` 文件，或修改代码中的文件路径。

### Q: API返回400错误怎么办？
A: 检查：
1. 环境变量配置是否正确
2. 音频文件格式是否为MP3
3. 网络连接是否正常
4. API配额是否充足

### Q: 如何测试不同的音频文件？
A: 修改 `asr_test.go` 中的 `audioFilePath` 变量，或者替换 `test_audio.mp3` 文件。

## 调试技巧

1. 查看详细日志：测试脚本会输出详细的调试信息
2. 检查API响应：注意观察HTTP状态码和响应内容
3. 验证音频格式：确保音频文件可以正常播放
4. 测试网络连接：可以先用curl测试API连通性

## 文件结构

```
test/
├── README.md           # 本说明文件
├── asr_test.go         # 测试程序
├── run_test.sh         # 运行脚本
├── .env.example        # 环境变量示例
├── .env.test           # 实际环境变量配置（需要创建）
└── test_audio.mp3      # 测试音频文件（需要准备）
```
