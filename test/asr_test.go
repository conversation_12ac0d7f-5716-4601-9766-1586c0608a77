package main

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

func main() {
	log.Println("=== 本地ASR服务测试脚本 ===")

	// 测试参数配置
	serverURL := "http://localhost:3000"
	audioFilePath := "test_audio.mp3"
	targetWord := "hello"

	// 检查服务是否运行
	log.Printf("检查本地服务是否运行在 %s ...", serverURL)
	if !checkServerHealth(serverURL) {
		log.Fatal("❌ 本地服务未运行，请先启动服务: go run main.go")
	}
	log.Printf("✓ 本地服务运行正常")

	// 检查音频文件是否存在
	if _, err := os.Stat(audioFilePath); os.IsNotExist(err) {
		log.Printf("❌ 测试音频文件 %s 不存在", audioFilePath)
		log.Printf("请在 test/ 目录下放置一个测试音频文件:")
		log.Printf("  - 文件名: test_audio.mp3")
		log.Printf("  - 格式: MP3/WAV/M4A")
		log.Printf("  - 大小: 小于10MB")
		log.Printf("  - 内容: 清晰的英文单词发音")
		return
	}

	// 读取音频文件
	log.Printf("读取音频文件: %s", audioFilePath)
	audioData, err := os.ReadFile(audioFilePath)
	if err != nil {
		log.Fatalf("❌ 读取音频文件失败: %v", err)
	}
	log.Printf("✓ 音频文件读取成功，大小: %d bytes", len(audioData))

	// 测试ASR接口
	log.Printf("开始测试ASR接口...")
	log.Printf("  - 服务地址: %s", serverURL)
	log.Printf("  - 音频文件: %s", audioFilePath)
	log.Printf("  - 目标单词: %s", targetWord)
	log.Printf("  - 音频大小: %d bytes", len(audioData))

	// 调用ASR API
	start := time.Now()
	result, err := callASRAPI(serverURL, audioFilePath, audioData, targetWord)
	duration := time.Since(start)

	log.Printf("=== 测试结果 ===")
	log.Printf("耗时: %v", duration)

	if err != nil {
		log.Printf("❌ 测试失败: %v", err)
		return
	}

	log.Printf("✓ 测试成功!")
	log.Printf("响应内容: %s", result)
}

// checkServerHealth 检查服务健康状态
func checkServerHealth(serverURL string) bool {
	client := &http.Client{Timeout: 5 * time.Second}

	resp, err := client.Get(serverURL + "/health")
	if err != nil {
		log.Printf("健康检查失败: %v", err)
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}

// callASRAPI 调用ASR API
func callASRAPI(serverURL, fileName string, audioData []byte, targetWord string) (string, error) {
	// 创建multipart form data
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加音频文件
	audioWriter, err := writer.CreateFormFile("audio", filepath.Base(fileName))
	if err != nil {
		return "", fmt.Errorf("创建音频字段失败: %v", err)
	}
	_, err = audioWriter.Write(audioData)
	if err != nil {
		return "", fmt.Errorf("写入音频数据失败: %v", err)
	}

	// 添加目标单词
	err = writer.WriteField("target_word", targetWord)
	if err != nil {
		return "", fmt.Errorf("写入目标单词失败: %v", err)
	}

	writer.Close()

	// 创建HTTP请求
	url := serverURL + "/api/v1/asr"
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	log.Printf("发送请求到: %s", url)

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	log.Printf("响应状态码: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return string(body), nil
}
