#!/bin/bash

# 简单的 curl 测试脚本

echo "=== 使用 curl 测试本地ASR服务 ==="

# 配置参数
SERVER_URL="http://localhost:3000"
AUDIO_FILE="test_audio.mp3"
TARGET_WORD="apple"

# 检查音频文件
if [ ! -f "$AUDIO_FILE" ]; then
    echo "❌ 音频文件 $AUDIO_FILE 不存在"
    echo "请准备一个测试音频文件"
    exit 1
fi

# 检查服务
echo "检查本地服务..."
if ! curl -s "$SERVER_URL/health" > /dev/null; then
    echo "❌ 本地服务未运行在 $SERVER_URL"
    echo "请先启动服务: go run main.go"
    exit 1
fi

echo "✓ 本地服务运行正常"
echo "音频文件: $AUDIO_FILE"
echo "目标单词: $TARGET_WORD"
echo ""

# 测试健康检查接口
echo "=== 测试健康检查接口 ==="
curl -s "$SERVER_URL/health" | jq . || curl -s "$SERVER_URL/health"
echo ""

# 测试ASR接口
echo "=== 测试ASR接口 ==="
echo "发送请求到: $SERVER_URL/api/v1/asr"

curl -X POST "$SERVER_URL/api/v1/asr" \
  -F "audio=@$AUDIO_FILE" \
  -F "target_word=$TARGET_WORD" \
  -w "\n状态码: %{http_code}\n耗时: %{time_total}s\n" \
  | jq . 2>/dev/null || echo "响应不是有效的JSON格式"

echo ""
echo "=== 测试完成 ==="
