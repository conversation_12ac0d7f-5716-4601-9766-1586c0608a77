#!/bin/bash

# 本地ASR服务测试脚本

echo "=== 本地ASR服务测试启动脚本 ==="

# 检查是否在正确的目录
if [ ! -f "../go.mod" ]; then
    echo "错误: 请在 test/ 目录下运行此脚本"
    exit 1
fi

# 检查是否有测试音频文件
if [ ! -f "test_audio.mp3" ]; then
    echo "警告: test_audio.mp3 不存在"
    echo "请准备一个测试音频文件并命名为 test_audio.mp3"
    echo "音频要求:"
    echo "  - 格式: MP3/WAV/M4A"
    echo "  - 大小: 小于10MB"
    echo "  - 内容: 清晰的英文单词发音"
    echo ""
fi

# 检查本地服务是否运行
echo "检查本地服务状态..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ 本地服务未运行在 localhost:3000"
    echo "请先启动本地服务:"
    echo "  cd .."
    echo "  go run main.go"
    echo ""
    echo "或者使用 make 命令 (如果有 Makefile):"
    echo "  make run"
    exit 1
fi

echo "✓ 本地服务运行正常"

# 运行测试
echo "开始运行 ASR 测试..."
cd ..
go run test/asr_test.go
