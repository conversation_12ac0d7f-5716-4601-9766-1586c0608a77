package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"guess-word-backend/internal/config"
	"guess-word-backend/internal/services"
)

func testASR() {
	log.Println("=== ASR 本地测试脚本 ===")

	// 加载配置
	cfg := config.Load()

	// 验证必需的环境变量
	if cfg.DoubaoSTTAppID == "" {
		log.Fatal("错误: DOUBAO_STT_APP_ID 环境变量未设置")
	}
	if cfg.DoubaoSTTToken == "" {
		log.Fatal("错误: DOUBAO_STT_ACCESS_TOKEN 环境变量未设置")
	}

	log.Printf("✓ 配置验证通过")
	log.Printf("  - App ID: %s", maskStringForDisplay(cfg.DoubaoSTTAppID, 10))
	log.Printf("  - Token: %s", maskStringForDisplay(cfg.DoubaoSTTToken, 10))

	// 创建ASR服务
	asrService := services.NewASRService(cfg)

	// 测试音频文件路径 - 你可以修改这个路径
	audioFilePath := "test_audio.mp3"

	// 检查音频文件是否存在
	if _, err := os.Stat(audioFilePath); os.IsNotExist(err) {
		log.Printf("警告: 测试音频文件 %s 不存在", audioFilePath)
		log.Printf("你可以:")
		log.Printf("1. 将音频文件放在项目根目录并命名为 test_audio.mp3")
		log.Printf("2. 修改 audioFilePath 变量指向你的音频文件")
		log.Printf("3. 使用下面的在线测试音频")

		// 尝试下载测试音频
		if err := downloadTestAudio(audioFilePath); err != nil {
			log.Printf("下载测试音频失败: %v", err)
			log.Printf("请手动准备音频文件后重新运行")
			return
		}
	}

	// 读取音频文件
	log.Printf("读取音频文件: %s", audioFilePath)
	audioData, err := os.ReadFile(audioFilePath)
	if err != nil {
		log.Fatalf("读取音频文件失败: %v", err)
	}

	log.Printf("✓ 音频文件读取成功，大小: %d bytes", len(audioData))

	// 目标单词 - 你可以修改这个
	targetWord := "hello"

	log.Printf("开始测试语音识别...")
	log.Printf("  - 目标单词: %s", targetWord)
	log.Printf("  - 音频大小: %d bytes", len(audioData))

	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 调用ASR服务
	start := time.Now()
	result, err := asrService.RecognizeSpeech(ctx, audioData, targetWord)
	duration := time.Since(start)

	log.Printf("=== 测试结果 ===")
	log.Printf("耗时: %v", duration)

	if err != nil {
		log.Printf("❌ 测试失败: %v", err)
		return
	}

	// 输出结果
	log.Printf("✓ 测试成功!")
	log.Printf("  - 识别成功: %t", result.Success)
	log.Printf("  - 识别文本: '%s'", result.Text)
	log.Printf("  - 置信度: %.2f", result.Confidence)
	log.Printf("  - 消息: %s", result.Message)

	if result.Error != "" {
		log.Printf("  - 错误: %s", result.Error)
		log.Printf("  - 错误代码: %s", result.Code)
	}

	// 评估结果
	log.Printf("=== 评估 ===")
	if result.Success {
		if result.Confidence >= 0.8 {
			log.Printf("🎉 识别效果很好! (置信度 >= 0.8)")
		} else if result.Confidence >= 0.5 {
			log.Printf("⚠️  识别效果一般 (置信度 >= 0.5)")
		} else {
			log.Printf("❌ 识别效果较差 (置信度 < 0.5)")
		}
	} else {
		log.Printf("❌ 识别失败")
	}
}

// maskStringForDisplay 遮盖字符串用于安全显示
func maskStringForDisplay(s string, showLen int) string {
	if len(s) <= showLen {
		return "***"
	}
	return s[:showLen] + "..."
}

// downloadTestAudio 下载测试音频文件
func downloadTestAudio(filePath string) error {
	log.Printf("尝试下载测试音频文件...")

	// 这里使用一个示例音频URL，你可以替换为实际的测试音频
	// 注意：这只是示例，实际使用时你需要准备自己的测试音频
	testAudioURL := "https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3"

	resp, err := http.Get(testAudioURL)
	if err != nil {
		return fmt.Errorf("下载失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存文件失败: %v", err)
	}

	log.Printf("✓ 测试音频下载成功: %s", filePath)
	return nil
}
